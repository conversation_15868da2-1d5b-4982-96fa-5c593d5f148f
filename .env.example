# Server Configuration
PORT=5096

# Twitch API Configuration (for user authentication)
TWITCH_CLIENT_ID=your_twitch_client_id_here
TWITCH_CLIENT_SECRET=your_twitch_client_secret_here

# IGDB API Configuration (for game search)
IGDB_CLIENT_ID=your_igdb_client_id_here
IGDB_CLIENT_SECRET=your_igdb_client_secret_here

# YouTube API Configuration (for playlist information)
# Get your API key from: https://console.developers.google.com/
YOUTUBE_API_KEY=your_youtube_api_key_here

# Config Server Integration (for syncing playlists from XenoVODs config)
CONFIG_SERVER_URL=http://localhost:3001

# Admin Users (comma-separated list of Twitch usernames)
ADMINS=username1,username2

# Session Secret (generate a random string)
SESSION_SECRET=your_random_session_secret_here
